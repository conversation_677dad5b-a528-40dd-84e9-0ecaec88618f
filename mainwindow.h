﻿#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QKeyEvent>
#include <QApplication>
#include <QScreen>
#include <QTimer>
#include <QMessageBox>
#include <QGuiApplication>
#include <QLabel>
#include <QDateTime>
#include <random>
#include <iostream>
#include <ctime>
#include <algorithm>

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
     void setupDVD();
     void moveDVD();
     void updateTimeDisplay();
     void checkPlatformSupport();

private:
    Ui::MainWindow *ui;
    QScreen *screen = QApplication::primaryScreen();
    QLabel *timeLabel;
    QTimer *displayTimer;
    bool canMoveWindow = true;
    int elapsedSeconds = 0;

    double iconX, iconY;  // 浮動小数点で位置を管理
    double velocityX, velocityY;  // 速度ベクトル
    enum Vertical {UP,DOWN}vert;
    enum Horizontal {LEFT,RIGHT}hor;
};
#endif // MAINWINDOW_H
